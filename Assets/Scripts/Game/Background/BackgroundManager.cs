using System.Collections.Generic;
using UnityEngine;

namespace Game.Background
{
    public class BackgroundManager : MonoBehaviour
    {
        private const string BackgroundPrefabPath = "Prefabs/Background/Background";

        private Dictionary<BattleBackgroundType, string> backgroundCache =
            new Dictionary<BattleBackgroundType, string>()
            {
                [BattleBackgroundType.None] = "",
                [BattleBackgroundType.Town] = "",
                [BattleBackgroundType.Ship] = "Background_Ship",
                [BattleBackgroundType.ShipNight] = "Background_Mine",
                [BattleBackgroundType.Jungle] = "Background_Market",
                [BattleBackgroundType.Mine] = "Background_Arena",
                [BattleBackgroundType.Tavern] = "Background_Tavern",
                [BattleBackgroundType.RiftShip] = "Background_Market_Great-Bazaar",
                [BattleBackgroundType.ShipElite] = "Background_GreatWave",
                [BattleBackgroundType.ShipKraken] = "Background_ShipHold",
                [BattleBackgroundType.Arena] = "Background_Ship_Rift",
                [BattleBackgroundType.ArenaNight] = "Background_Square",
                [BattleBackgroundType.ArenaBoss] = "Background_Water",
                [BattleBackgroundType.ArenaWater] = "Background_Waters",
            };

        public void SetBackground(BattleBackgroundType type, Transform parent)
        {
            if (backgroundCache.TryGetValue(type, out string backgroundPath))
            {
                ResManager.Instance.LoadAsync<GameObject>(backgroundPath, (go) =>
                {
                    if (go != null)
                    {
                        go.transform.SetParent(parent, false);
                    }
                },parent);
            }
        }
    }
}